#!/usr/bin/env deno run --allow-net --allow-env

/**
 * OpenAI Function Call Proxy Server
 * 基于Cherry Studio的XML格式模拟，让任意模型支持Function Call
 */

import { serve } from "https://deno.land/std@0.208.0/http/server.ts";

interface OpenAIMessage {
  role: "system" | "user" | "assistant" | "tool";
  content: string | null | OpenAIMessageContent[];
  tool_calls?: ToolCall[];
  tool_call_id?: string;
  name?: string;
}

interface OpenAIMessageContent {
  type: "text" | "image_url" | "input_audio";
  text?: string;
  image_url?: {
    url: string;
    detail?: "auto" | "low" | "high";
  };
  input_audio?: {
    data: string;
    format: "wav" | "mp3";
  };
}

interface ToolCall {
  id: string;
  type: "function";
  function: {
    name: string;
    arguments: string;
  };
}

interface FunctionDefinition {
  name: string;
  description: string;
  parameters: Record<string, any>;
}

interface ChatCompletionRequest {
  model: string;
  messages: OpenAIMessage[];
  tools?: { type: "function"; function: FunctionDefinition }[];
  temperature?: number;
  max_tokens?: number;
  stream?: boolean;
}

interface ToolUseMatch {
  toolName: string;
  arguments: string;
  fullMatch: string;
}

class FunctionCallProxy {
  constructor() {
    // 移除硬编码配置，改为从请求中动态解析
  }

  /**
   * 从请求URL解析上游服务地址
   */
  private parseUpstreamUrl(requestUrl: string): string {
    const url = new URL(requestUrl);
    // 支持多种URL格式：
    // 1. /proxy/{upstream_host}/v1/chat/completions
    // 2. /{upstream_host}/v1/chat/completions
    // 3. /v1/chat/completions?upstream={upstream_url}

    const pathParts = url.pathname.split('/').filter(p => p);

    // 格式1: /proxy/{upstream_host}/v1/chat/completions
    if (pathParts[0] === 'proxy' && pathParts.length >= 4) {
      const upstreamHost = pathParts[1];
      return this.reconstructUpstreamUrl(upstreamHost);
    }

    // 格式2: /{upstream_host}/v1/chat/completions
    if (pathParts.length >= 3 && pathParts[1] === 'v1') {
      const upstreamHost = pathParts[0];
      return this.reconstructUpstreamUrl(upstreamHost);
    }

    // 格式3: 查询参数
    const upstreamParam = url.searchParams.get('upstream');
    if (upstreamParam) {
      return upstreamParam;
    }

    // 默认使用OpenAI
    return 'https://api.openai.com';
  }

  /**
   * 重构上游URL
   */
  private reconstructUpstreamUrl(host: string): string {
    // 处理常见的服务商简写
    const serviceMap: Record<string, string> = {
      'openai': 'https://api.openai.com',
      'anthropic': 'https://api.anthropic.com',
      'claude': 'https://api.anthropic.com',
      'gemini': 'https://generativelanguage.googleapis.com',
      'ollama': 'http://localhost:11434',
      'lmstudio': 'http://localhost:1234',
    };

    if (serviceMap[host.toLowerCase()]) {
      return serviceMap[host.toLowerCase()];
    }

    // 如果包含协议，直接使用
    if (host.startsWith('http://') || host.startsWith('https://')) {
      return host;
    }

    // 否则假设是HTTPS
    return `https://${host}`;
  }

  /**
   * 从请求头获取API密钥
   */
  private extractApiKey(headers: Headers): string {
    const authHeader = headers.get('Authorization');
    if (!authHeader) {
      throw new Error('Missing Authorization header');
    }

    if (authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    throw new Error('Invalid Authorization header format. Expected: Bearer <token>');
  }

  /**
   * 生成System Prompt，包含工具使用说明
   */
  private generateSystemPrompt(tools: FunctionDefinition[]): string {
    const toolsXml = tools.map(tool => `
<tool>
  <name>${tool.name}</name>
  <description>${tool.description}</description>
  <parameters>${JSON.stringify(tool.parameters)}</parameters>
</tool>`).join('\n');

    return `You have access to a set of tools that you can use to help answer the user's question. When you need to use a tool, format your response using XML-style tags as follows:

<tool_use>
  <name>{tool_name}</name>
  <arguments>{json_arguments}</arguments>
</tool_use>

## Available Tools
<tools>
${toolsXml}
</tools>

## Tool Use Rules
1. Use the exact tool name as specified in the available tools list
2. Provide arguments as a valid JSON object
3. Only use tools when necessary to answer the user's question
4. You can use multiple tools in sequence if needed
5. Always use the XML format exactly as shown above

## Examples
User: What's the weather like in Tokyo?
Assistant: I'll check the weather in Tokyo for you.

<tool_use>
  <name>get_weather</name>
  <arguments>{"location": "Tokyo", "unit": "celsius"}</arguments>
</tool_use>

User: Calculate 15 * 23 and then convert the result to binary
Assistant: I'll calculate 15 * 23 first and then convert the result to binary.

<tool_use>
  <name>calculate</name>
  <arguments>{"expression": "15 * 23"}</arguments>
</tool_use>

Now I'll convert the result to binary:

<tool_use>
  <name>convert_base</name>
  <arguments>{"number": 345, "from_base": 10, "to_base": 2}</arguments>
</tool_use>

If you don't need to use any tools, just respond normally without the XML tags.`;
  }

  /**
   * 解析响应中的工具调用
   */
  private parseToolUse(content: string): ToolUseMatch[] {
    const toolUsePattern = /<tool_use>\s*<name>(.*?)<\/name>\s*<arguments>(.*?)<\/arguments>\s*<\/tool_use>/gs;
    const matches: ToolUseMatch[] = [];
    let match;

    while ((match = toolUsePattern.exec(content)) !== null) {
      matches.push({
        toolName: match[1].trim(),
        arguments: match[2].trim(),
        fullMatch: match[0]
      });
    }

    return matches;
  }

  /**
   * 生成工具调用ID
   */
  private generateToolCallId(): string {
    return `call_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取消息的文本内容
   */
  private getMessageTextContent(content: string | null | OpenAIMessageContent[]): string {
    if (typeof content === 'string') {
      return content || '';
    }
    if (Array.isArray(content)) {
      return content
        .filter(item => item.type === 'text')
        .map(item => item.text || '')
        .join('\n');
    }
    return '';
  }

  /**
   * 转换消息格式，处理工具调用
   */
  private transformMessages(messages: OpenAIMessage[], tools?: { type: "function"; function: FunctionDefinition }[]): OpenAIMessage[] {
    const transformedMessages: OpenAIMessage[] = [];

    // 如果有工具定义，添加system prompt
    if (tools && tools.length > 0) {
      const functionDefs = tools.map(t => t.function);
      const systemPrompt = this.generateSystemPrompt(functionDefs);

      // 查找现有的system消息
      const existingSystemIndex = messages.findIndex(m => m.role === "system");
      if (existingSystemIndex >= 0) {
        const existingContent = this.getMessageTextContent(messages[existingSystemIndex].content);
        // 合并system消息
        transformedMessages.push({
          role: "system",
          content: `${existingContent}\n\n${systemPrompt}`
        });
        // 跳过原始system消息
        messages = messages.filter((_, i) => i !== existingSystemIndex);
      } else {
        // 添加新的system消息
        transformedMessages.push({
          role: "system",
          content: systemPrompt
        });
      }
    }

    // 处理其他消息
    for (const message of messages) {
      if (message.role === "tool") {
        // 将tool消息转换为user消息
        const toolContent = this.getMessageTextContent(message.content);
        transformedMessages.push({
          role: "user",
          content: `<tool_result>
  <name>${message.name}</name>
  <result>${toolContent}</result>
</tool_result>`
        });
      } else if (message.role === "assistant" && message.tool_calls) {
        // 将tool_calls转换为XML格式
        let content = this.getMessageTextContent(message.content);
        for (const toolCall of message.tool_calls) {
          content += `\n\n<tool_use>
  <name>${toolCall.function.name}</name>
  <arguments>${toolCall.function.arguments}</arguments>
</tool_use>`;
        }
        transformedMessages.push({
          role: "assistant",
          content: content.trim()
        });
      } else {
        transformedMessages.push(message);
      }
    }

    return transformedMessages;
  }

  /**
   * 处理聊天完成请求
   */
  async handleChatCompletion(
    request: ChatCompletionRequest,
    requestUrl: string,
    requestHeaders: Headers
  ): Promise<Response> {
    try {
      // 从请求中解析上游URL和API密钥
      const upstreamUrl = this.parseUpstreamUrl(requestUrl);
      const apiKey = this.extractApiKey(requestHeaders);

      // 转换消息格式
      const transformedMessages = this.transformMessages(request.messages, request.tools);

      // 构建上游请求
      const upstreamRequest = {
        ...request,
        messages: transformedMessages,
        tools: undefined, // 移除tools参数，因为我们用system prompt代替
      };

      // 发送请求到上游服务
      const upstreamResponse = await fetch(`${upstreamUrl}/v1/chat/completions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`,
        },
        body: JSON.stringify(upstreamRequest),
      });

      if (!upstreamResponse.ok) {
        return new Response(await upstreamResponse.text(), {
          status: upstreamResponse.status,
          headers: { "Content-Type": "application/json" },
        });
      }

      const responseData = await upstreamResponse.json();

      // 处理响应，检查是否包含工具调用
      if (responseData.choices && responseData.choices[0]?.message?.content) {
        const content = responseData.choices[0].message.content;
        const toolUses = this.parseToolUse(content);

        if (toolUses.length > 0) {
          // 转换为OpenAI格式的tool_calls
          const toolCalls: ToolCall[] = toolUses.map(toolUse => ({
            id: this.generateToolCallId(),
            type: "function",
            function: {
              name: toolUse.toolName,
              arguments: toolUse.arguments
            }
          }));

          // 移除XML标签，保留其他内容
          let cleanContent = content;
          for (const toolUse of toolUses) {
            cleanContent = cleanContent.replace(toolUse.fullMatch, "").trim();
          }

          responseData.choices[0].message = {
            role: "assistant",
            content: cleanContent || null,
            tool_calls: toolCalls
          };
        }
      }

      return new Response(JSON.stringify(responseData), {
        headers: { "Content-Type": "application/json" },
      });

    } catch (error) {
      console.error("Error processing request:", error);
      return new Response(JSON.stringify({
        error: {
          message: error instanceof Error ? error.message : "Internal server error",
          type: "internal_error",
          code: "internal_error"
        }
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }
  }
}

// 主服务器逻辑
async function main() {
  const port = parseInt(Deno.env.get("PORT") || "8000");

  const proxy = new FunctionCallProxy();

  console.log(`🚀 Function Call Proxy Server starting on port ${port}`);
  console.log(`🔧 Ready to proxy function calls for any model!`);
  console.log(`� Supported URL formats:`);
  console.log(`   - /proxy/{service}/v1/chat/completions`);
  console.log(`   - /{service}/v1/chat/completions`);
  console.log(`   - /v1/chat/completions?upstream={url}`);
  console.log(`� Examples:`);
  console.log(`   - /proxy/openai/v1/chat/completions`);
  console.log(`   - /anthropic/v1/chat/completions`);
  console.log(`   - /v1/chat/completions?upstream=https://api.openai.com`);

  await serve(async (req: Request) => {
    const url = new URL(req.url);

    // 处理CORS
    if (req.method === "OPTIONS") {
      return new Response(null, {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization",
        },
      });
    }

    // 健康检查端点
    if (url.pathname === "/health" && req.method === "GET") {
      return new Response(JSON.stringify({
        status: "healthy",
        timestamp: new Date().toISOString(),
        version: "1.0.0",
        features: ["function_call_proxy", "multi_upstream", "dynamic_routing"]
      }), {
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*"
        },
      });
    }

    // 处理chat/completions端点（支持多种路径格式）
    const isChatCompletions = url.pathname.endsWith("/v1/chat/completions") ||
                             url.pathname === "/v1/chat/completions";

    if (isChatCompletions && req.method === "POST") {
      try {
        const requestData = await req.json() as ChatCompletionRequest;
        const response = await proxy.handleChatCompletion(requestData, req.url, req.headers);

        // 添加CORS头
        const headers = new Headers(response.headers);
        headers.set("Access-Control-Allow-Origin", "*");

        return new Response(response.body, {
          status: response.status,
          headers,
        });
      } catch (error) {
        console.error("Error parsing request:", error);
        return new Response(JSON.stringify({
          error: {
            message: "Invalid JSON in request body",
            type: "invalid_request_error",
            code: "invalid_json"
          }
        }), {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*"
          },
        });
      }
    }

    // 返回API使用说明
    if (url.pathname === "/" && req.method === "GET") {
      return new Response(JSON.stringify({
        name: "OpenAI Function Call Proxy",
        version: "1.0.0",
        description: "Universal proxy to add function calling support to any OpenAI-compatible API",
        endpoints: {
          health: "/health",
          chat: [
            "/proxy/{service}/v1/chat/completions",
            "/{service}/v1/chat/completions",
            "/v1/chat/completions?upstream={url}"
          ]
        },
        supported_services: ["openai", "anthropic", "claude", "gemini", "ollama", "lmstudio"],
        usage: {
          authorization: "Bearer <your-api-key>",
          content_type: "application/json"
        }
      }), {
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*"
        },
      });
    }

    // 其他请求返回404
    return new Response(JSON.stringify({
      error: {
        message: "Not Found",
        type: "not_found_error",
        code: "not_found"
      }
    }), {
      status: 404,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*"
      }
    });
  }, { port });
}

if (import.meta.main) {
  main();
}
