#!/usr/bin/env deno run --allow-net --allow-env

/**
 * OpenAI Function Call Proxy Server
 * 基于Cherry Studio的XML格式模拟，让任意模型支持Function Call
 */

import { serve } from "https://deno.land/std@0.208.0/http/server.ts";

interface OpenAIMessage {
  role: "system" | "user" | "assistant" | "tool";
  content: string | null;
  tool_calls?: ToolCall[];
  tool_call_id?: string;
  name?: string;
}

interface ToolCall {
  id: string;
  type: "function";
  function: {
    name: string;
    arguments: string;
  };
}

interface FunctionDefinition {
  name: string;
  description: string;
  parameters: Record<string, any>;
}

interface ChatCompletionRequest {
  model: string;
  messages: OpenAIMessage[];
  tools?: { type: "function"; function: FunctionDefinition }[];
  temperature?: number;
  max_tokens?: number;
  stream?: boolean;
}

interface ToolUseMatch {
  toolName: string;
  arguments: string;
  fullMatch: string;
}

class FunctionCallProxy {
  private upstreamUrl: string;
  private apiKey: string;

  constructor(upstreamUrl: string, apiKey: string) {
    this.upstreamUrl = upstreamUrl;
    this.apiKey = apiKey;
  }

  /**
   * 生成System Prompt，包含工具使用说明
   */
  private generateSystemPrompt(tools: FunctionDefinition[]): string {
    const toolsXml = tools.map(tool => `
<tool>
  <name>${tool.name}</name>
  <description>${tool.description}</description>
  <parameters>${JSON.stringify(tool.parameters)}</parameters>
</tool>`).join('\n');

    return `You have access to a set of tools that you can use to help answer the user's question. When you need to use a tool, format your response using XML-style tags as follows:

<tool_use>
  <name>{tool_name}</name>
  <arguments>{json_arguments}</arguments>
</tool_use>

## Available Tools
<tools>
${toolsXml}
</tools>

## Tool Use Rules
1. Use the exact tool name as specified in the available tools list
2. Provide arguments as a valid JSON object
3. Only use tools when necessary to answer the user's question
4. You can use multiple tools in sequence if needed
5. Always use the XML format exactly as shown above

## Examples
User: What's the weather like in Tokyo?
Assistant: I'll check the weather in Tokyo for you.

<tool_use>
  <name>get_weather</name>
  <arguments>{"location": "Tokyo", "unit": "celsius"}</arguments>
</tool_use>

User: Calculate 15 * 23 and then convert the result to binary
Assistant: I'll calculate 15 * 23 first and then convert the result to binary.

<tool_use>
  <name>calculate</name>
  <arguments>{"expression": "15 * 23"}</arguments>
</tool_use>

Now I'll convert the result to binary:

<tool_use>
  <name>convert_base</name>
  <arguments>{"number": 345, "from_base": 10, "to_base": 2}</arguments>
</tool_use>

If you don't need to use any tools, just respond normally without the XML tags.`;
  }

  /**
   * 解析响应中的工具调用
   */
  private parseToolUse(content: string): ToolUseMatch[] {
    const toolUsePattern = /<tool_use>\s*<name>(.*?)<\/name>\s*<arguments>(.*?)<\/arguments>\s*<\/tool_use>/gs;
    const matches: ToolUseMatch[] = [];
    let match;

    while ((match = toolUsePattern.exec(content)) !== null) {
      matches.push({
        toolName: match[1].trim(),
        arguments: match[2].trim(),
        fullMatch: match[0]
      });
    }

    return matches;
  }

  /**
   * 生成工具调用ID
   */
  private generateToolCallId(): string {
    return `call_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 转换消息格式，处理工具调用
   */
  private transformMessages(messages: OpenAIMessage[], tools?: { type: "function"; function: FunctionDefinition }[]): OpenAIMessage[] {
    const transformedMessages: OpenAIMessage[] = [];
    
    // 如果有工具定义，添加system prompt
    if (tools && tools.length > 0) {
      const functionDefs = tools.map(t => t.function);
      const systemPrompt = this.generateSystemPrompt(functionDefs);
      
      // 查找现有的system消息
      const existingSystemIndex = messages.findIndex(m => m.role === "system");
      if (existingSystemIndex >= 0) {
        // 合并system消息
        transformedMessages.push({
          role: "system",
          content: `${messages[existingSystemIndex].content}\n\n${systemPrompt}`
        });
        // 跳过原始system消息
        messages = messages.filter((_, i) => i !== existingSystemIndex);
      } else {
        // 添加新的system消息
        transformedMessages.push({
          role: "system",
          content: systemPrompt
        });
      }
    }

    // 处理其他消息
    for (const message of messages) {
      if (message.role === "tool") {
        // 将tool消息转换为user消息
        transformedMessages.push({
          role: "user",
          content: `<tool_result>
  <name>${message.name}</name>
  <result>${message.content}</result>
</tool_result>`
        });
      } else if (message.role === "assistant" && message.tool_calls) {
        // 将tool_calls转换为XML格式
        let content = message.content || "";
        for (const toolCall of message.tool_calls) {
          content += `\n\n<tool_use>
  <name>${toolCall.function.name}</name>
  <arguments>${toolCall.function.arguments}</arguments>
</tool_use>`;
        }
        transformedMessages.push({
          role: "assistant",
          content: content.trim()
        });
      } else {
        transformedMessages.push(message);
      }
    }

    return transformedMessages;
  }

  /**
   * 处理聊天完成请求
   */
  async handleChatCompletion(request: ChatCompletionRequest): Promise<Response> {
    try {
      // 转换消息格式
      const transformedMessages = this.transformMessages(request.messages, request.tools);
      
      // 构建上游请求
      const upstreamRequest = {
        ...request,
        messages: transformedMessages,
        tools: undefined, // 移除tools参数，因为我们用system prompt代替
      };

      // 发送请求到上游服务
      const upstreamResponse = await fetch(`${this.upstreamUrl}/v1/chat/completions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify(upstreamRequest),
      });

      if (!upstreamResponse.ok) {
        return new Response(await upstreamResponse.text(), {
          status: upstreamResponse.status,
          headers: { "Content-Type": "application/json" },
        });
      }

      const responseData = await upstreamResponse.json();
      
      // 处理响应，检查是否包含工具调用
      if (responseData.choices && responseData.choices[0]?.message?.content) {
        const content = responseData.choices[0].message.content;
        const toolUses = this.parseToolUse(content);
        
        if (toolUses.length > 0) {
          // 转换为OpenAI格式的tool_calls
          const toolCalls: ToolCall[] = toolUses.map(toolUse => ({
            id: this.generateToolCallId(),
            type: "function",
            function: {
              name: toolUse.toolName,
              arguments: toolUse.arguments
            }
          }));

          // 移除XML标签，保留其他内容
          let cleanContent = content;
          for (const toolUse of toolUses) {
            cleanContent = cleanContent.replace(toolUse.fullMatch, "").trim();
          }

          responseData.choices[0].message = {
            role: "assistant",
            content: cleanContent || null,
            tool_calls: toolCalls
          };
        }
      }

      return new Response(JSON.stringify(responseData), {
        headers: { "Content-Type": "application/json" },
      });

    } catch (error) {
      console.error("Error processing request:", error);
      return new Response(JSON.stringify({
        error: {
          message: "Internal server error",
          type: "internal_error",
          code: "internal_error"
        }
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }
  }
}

// 主服务器逻辑
async function main() {
  const port = parseInt(Deno.env.get("PORT") || "8000");
  const upstreamUrl = Deno.env.get("UPSTREAM_URL") || "https://api.openai.com";
  const apiKey = Deno.env.get("API_KEY") || "";

  if (!apiKey) {
    console.error("API_KEY environment variable is required");
    Deno.exit(1);
  }

  const proxy = new FunctionCallProxy(upstreamUrl, apiKey);

  console.log(`🚀 Function Call Proxy Server starting on port ${port}`);
  console.log(`📡 Upstream URL: ${upstreamUrl}`);
  console.log(`🔧 Ready to proxy function calls for any model!`);

  await serve(async (req: Request) => {
    const url = new URL(req.url);
    
    // 处理CORS
    if (req.method === "OPTIONS") {
      return new Response(null, {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization",
        },
      });
    }

    // 健康检查端点
    if (url.pathname === "/health" && req.method === "GET") {
      return new Response(JSON.stringify({
        status: "healthy",
        timestamp: new Date().toISOString(),
        uptime: process.uptime?.() || 0
      }), {
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*"
        },
      });
    }

    // 只处理chat/completions端点
    if (url.pathname === "/v1/chat/completions" && req.method === "POST") {
      try {
        const requestData = await req.json() as ChatCompletionRequest;
        const response = await proxy.handleChatCompletion(requestData);

        // 添加CORS头
        const headers = new Headers(response.headers);
        headers.set("Access-Control-Allow-Origin", "*");

        return new Response(response.body, {
          status: response.status,
          headers,
        });
      } catch (error) {
        console.error("Error parsing request:", error);
        return new Response(JSON.stringify({
          error: {
            message: "Invalid JSON in request body",
            type: "invalid_request_error",
            code: "invalid_json"
          }
        }), {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*"
          },
        });
      }
    }

    // 其他请求直接代理
    return new Response("Not Found", { status: 404 });
  }, { port });
}

if (import.meta.main) {
  main();
}
