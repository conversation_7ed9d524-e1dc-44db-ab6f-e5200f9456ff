# OpenAI Function Call Proxy

基于Cherry Studio的XML格式模拟技术，让任意模型都支持OpenAI Function Call功能的代理服务器。

## 🌟 特性

- **通用兼容性**: 让任何支持OpenAI v1 API的模型都能使用Function Call
- **XML格式模拟**: 基于Cherry Studio的成熟方案，通过System Prompt + XML解析实现
- **完全兼容**: 完全兼容OpenAI v1 chat/completions接口
- **零配置**: 开箱即用，只需设置上游API地址和密钥
- **高性能**: 轻量级代理，最小化延迟开销
- **错误处理**: 完善的错误处理和容错机制

## 🚀 快速开始

### 1. 启动代理服务器

```bash
# 设置环境变量
export UPSTREAM_URL="https://api.openai.com"  # 或其他兼容OpenAI API的服务
export API_KEY="your-api-key"
export PORT="8000"

# 启动服务器
deno run --allow-net --allow-env openai_function_call_proxy.ts
```

### 2. 使用代理服务

现在你可以像使用OpenAI API一样使用任何模型的Function Call功能：

```typescript
const response = await fetch("http://localhost:8000/v1/chat/completions", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    "Authorization": "Bearer your-api-key"
  },
  body: JSON.stringify({
    model: "any-model-name",  // 任何模型都可以
    messages: [
      { role: "user", content: "What's the weather like in Tokyo?" }
    ],
    tools: [
      {
        type: "function",
        function: {
          name: "get_weather",
          description: "Get current weather information",
          parameters: {
            type: "object",
            properties: {
              location: { type: "string", description: "City name" },
              unit: { type: "string", enum: ["celsius", "fahrenheit"] }
            },
            required: ["location"]
          }
        }
      }
    ]
  })
});
```

## 🔧 工作原理

### 1. 请求转换

代理服务器接收标准的OpenAI Function Call请求，并进行以下转换：

1. **提取工具定义**: 从`tools`参数中提取函数定义
2. **生成System Prompt**: 创建包含工具使用说明的系统提示词
3. **消息格式转换**: 将tool消息转换为XML格式的用户消息

### 2. XML格式规范

模型需要按以下格式生成工具调用：

```xml
<tool_use>
  <name>function_name</name>
  <arguments>{"param1": "value1", "param2": "value2"}</arguments>
</tool_use>
```

### 3. 响应解析

代理服务器解析模型响应中的XML标签，并转换为标准的OpenAI tool_calls格式。

## 📝 配置选项

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `UPSTREAM_URL` | 上游API服务地址 | `https://api.openai.com` |
| `API_KEY` | API密钥 | 必需 |
| `PORT` | 服务器端口 | `8000` |

### 支持的上游服务

- OpenAI API
- Azure OpenAI
- Anthropic Claude (通过兼容层)
- 本地模型服务 (Ollama, LM Studio等)
- 任何兼容OpenAI v1 API的服务

## 🧪 测试

运行测试脚本验证功能：

```bash
# 基本功能测试
deno run --allow-net example_usage.ts test

# 完整流程模拟
deno run --allow-net example_usage.ts simulate

# 自定义服务器地址测试
deno run --allow-net example_usage.ts test http://localhost:8000
```

## 📋 使用示例

### 基础工具调用

```typescript
const weatherRequest = {
  model: "gpt-3.5-turbo",
  messages: [
    { role: "user", content: "What's the weather in Tokyo?" }
  ],
  tools: [
    {
      type: "function",
      function: {
        name: "get_weather",
        description: "Get weather information",
        parameters: {
          type: "object",
          properties: {
            location: { type: "string" },
            unit: { type: "string", enum: ["celsius", "fahrenheit"] }
          },
          required: ["location"]
        }
      }
    }
  ]
};
```

### 多步骤工具调用

```typescript
const calculationRequest = {
  model: "claude-3-sonnet",
  messages: [
    { role: "user", content: "Calculate 15 * 23 and convert to binary" }
  ],
  tools: [
    {
      type: "function",
      function: {
        name: "calculate",
        description: "Perform calculations",
        parameters: {
          type: "object",
          properties: {
            expression: { type: "string" }
          },
          required: ["expression"]
        }
      }
    },
    {
      type: "function",
      function: {
        name: "convert_base",
        description: "Convert number base",
        parameters: {
          type: "object",
          properties: {
            number: { type: "number" },
            from_base: { type: "number" },
            to_base: { type: "number" }
          },
          required: ["number", "from_base", "to_base"]
        }
      }
    }
  ]
};
```

### 处理工具调用结果

```typescript
// 1. 发送初始请求，获取工具调用
const response1 = await fetch("/v1/chat/completions", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify(weatherRequest)
});

const result1 = await response1.json();
const toolCall = result1.choices[0].message.tool_calls[0];

// 2. 执行工具调用 (你的业务逻辑)
const weatherData = await executeWeatherTool(toolCall.function.arguments);

// 3. 发送工具结果，获取最终回复
const response2 = await fetch("/v1/chat/completions", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    model: "gpt-3.5-turbo",
    messages: [
      { role: "user", content: "What's the weather in Tokyo?" },
      result1.choices[0].message,  // 包含tool_calls的助手消息
      {
        role: "tool",
        tool_call_id: toolCall.id,
        name: toolCall.function.name,
        content: JSON.stringify(weatherData)
      }
    ],
    tools: weatherRequest.tools
  })
});
```

## 🔍 调试

启用详细日志：

```bash
# 启动时查看详细输出
deno run --allow-net --allow-env openai_function_call_proxy.ts
```

服务器会输出：
- 请求转换过程
- XML解析结果
- 错误信息和堆栈跟踪

## ⚠️ 注意事项

1. **模型能力**: 不同模型对XML格式的理解能力可能不同，建议使用较新的大模型
2. **Token消耗**: System Prompt会增加token消耗，特别是工具数量较多时
3. **解析准确性**: XML解析依赖模型严格按照格式输出，可能存在解析失败的情况
4. **性能考虑**: 相比原生Function Call，会有额外的文本处理开销

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

## 🙏 致谢

本项目基于[Cherry Studio](https://github.com/CherryHQ/cherry-studio)的Function Call实现方案，感谢Cherry Studio团队的优秀工作。
